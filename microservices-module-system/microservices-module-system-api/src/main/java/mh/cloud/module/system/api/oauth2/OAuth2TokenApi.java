package mh.cloud.module.system.api.oauth2;

import mh.cloud.framework.common.pojo.CommonResult;
import mh.cloud.module.system.api.oauth2.dto.OAuth2AccessTokenCheckRespDTO;
import mh.cloud.module.system.api.oauth2.dto.OAuth2AccessTokenCreateReqDTO;
import mh.cloud.module.system.api.oauth2.dto.OAuth2AccessTokenRespDTO;
import mh.cloud.module.system.enums.ApiConstants;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.Operation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;

@FeignClient(name = ApiConstants.NAME, url = "#{T(mh.cloud.module.system.enums.ApiConstants).url}") // TODO luohang：fallbackFactory =
@Tag(name = "RPC 服务 - OAuth2.0 令牌")
public interface OAuth2TokenApi {

    String PREFIX = ApiConstants.PREFIX + "/oauth2/token";

    /**
     * 校验 Token 的 URL 地址，主要是提供给 Gateway 使用
     */
    @SuppressWarnings("HttpUrlsUsage")
    String NACOS_URL_CHECK = "http://" + ApiConstants.NAME + PREFIX + "/check";
    String URL_CHECK = ApiConstants.url + PREFIX + "/check";
    String NACOS_URL_CHECK_WITH_IP = "http://" + ApiConstants.NAME + PREFIX + "/check-with-ip";
    String URL_CHECK_WITH_IP = ApiConstants.url + PREFIX + "/check-with-ip";

    @PostMapping(PREFIX + "/create")
    @Operation(summary = "创建访问令牌")
    CommonResult<OAuth2AccessTokenRespDTO> createAccessToken(@Valid @RequestBody OAuth2AccessTokenCreateReqDTO reqDTO);

    @GetMapping(PREFIX + "/check")
    @Operation(summary = "校验访问令牌")
    @Parameter(name = "accessToken", description = "访问令牌", required = true, example = "tudou")
    CommonResult<OAuth2AccessTokenCheckRespDTO> checkAccessToken(@RequestParam("accessToken") String accessToken);

    @GetMapping(PREFIX + "/check-with-ip")
    @Operation(summary = "校验访问令牌和IP地址")
    @Parameter(name = "accessToken", description = "访问令牌", required = true, example = "tudou")
    @Parameter(name = "clientIP", description = "客户端IP地址", required = true, example = "***********")
    CommonResult<OAuth2AccessTokenCheckRespDTO> checkAccessTokenWithIP(@RequestParam("accessToken") String accessToken,
                                                                        @RequestParam("clientIP") String clientIP);

    @PostMapping(PREFIX + "/remove")
    @Operation(summary = "移除访问令牌")
    @Parameter(name = "accessToken", description = "访问令牌", required = true, example = "tudou")
    CommonResult<OAuth2AccessTokenRespDTO> removeAccessToken(@RequestParam("accessToken") String accessToken);

    @PostMapping(PREFIX + "/refresh")
    @Operation(summary = "刷新访问令牌")
    @Parameters({
        @Parameter(name = "refreshToken", description = "刷新令牌", required = true, example = "haha"),
        @Parameter(name = "clientId", description = "客户端编号", required = true, example = "microservicesyuanma")
    })
    CommonResult<OAuth2AccessTokenRespDTO> refreshAccessToken(@RequestParam("refreshToken") String refreshToken,
                                                              @RequestParam("clientId") String clientId);

}
