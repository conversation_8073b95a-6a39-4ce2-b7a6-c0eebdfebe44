# IP验证功能测试说明

## 功能概述
系统现在实现了IP地址验证功能，除登录接口外的所有接口都会验证客户端IP地址与数据库中存储的IP地址是否匹配。

## 实现的功能

### 1. Token验证时的IP检查
- 在`TokenAuthenticationFilter`中，所有需要token验证的接口都会同时验证IP地址
- 如果IP地址不匹配，会抛出"访问令牌不存在或IP地址不匹配，请重新登录"的异常

### 2. 数据库存储
- `OAuth2AccessTokenDO`实体类中的`userIp`字段存储用户登录时的IP地址
- 登录时会自动保存客户端IP到token记录中

### 3. 网关支持
- 网关中的`TokenAuthenticationFilter`也支持IP验证
- 缓存key包含IP信息，确保不同IP的用户不会共享缓存

### 4. 登录接口排除
- 登录相关接口使用`@PermitAll`注解，不会进行token验证，因此不受IP验证影响

## 测试步骤

### 1. 正常登录测试
```bash
# 1. 用户正常登录
POST /admin-api/system/auth/login
{
  "username": "admin",
  "password": "admin123"
}

# 2. 使用返回的token访问其他接口（应该成功）
GET /admin-api/system/auth/get-permission-info
Authorization: Bearer <token>
```

### 2. IP变更测试
```bash
# 1. 用户在IP A登录获取token
# 2. 模拟从IP B使用相同token访问接口（应该失败）
# 可以通过修改请求头中的X-Forwarded-For来模拟不同IP
GET /admin-api/system/auth/get-permission-info
Authorization: Bearer <token>
X-Forwarded-For: *************
```

### 3. 重新登录测试
```bash
# 1. IP变更后访问失败
# 2. 重新登录获取新token（应该成功）
# 3. 使用新token访问接口（应该成功）
```

## 关键修改文件

1. **TokenAuthenticationFilter.java** - 添加IP验证逻辑
2. **OAuth2TokenApi.java** - 添加带IP验证的接口方法
3. **OAuth2TokenService.java** - 添加IP验证服务方法
4. **OAuth2TokenServiceImpl.java** - 实现IP验证逻辑
5. **OAuth2TokenApiImpl.java** - 实现API接口
6. **网关TokenAuthenticationFilter.java** - 网关IP验证支持

## 安全特性

1. **IP绑定**: Token与登录时的IP地址绑定
2. **自动失效**: IP不匹配时token自动失效
3. **强制重登**: 需要重新登录获取新token
4. **缓存安全**: 网关缓存包含IP信息，防止跨IP访问

## 特殊接口处理

### 不进行IP验证的接口
以下接口使用`@PermitAll`注解，不会经过TokenAuthenticationFilter，因此不进行IP验证：

1. **登录相关接口** - `/admin-api/system/auth/login`等
2. **OAuth2开放接口** - `/admin-api/system/oauth2/open/check-token`等
3. **特殊验证接口** - `/admin-api/system/portal/cursessionOA2`等

### 需要特殊考虑的接口
1. **WebSocket连接** - 目前暂时不进行IP验证，因为WebSocket获取客户端IP较复杂
2. **第三方系统调用** - 某些外部系统调用的接口可能需要特殊处理

## 注意事项

1. 确保客户端IP获取逻辑正确处理代理和负载均衡器
2. 登录接口不受IP验证影响
3. WebSocket等特殊连接暂时不进行IP验证
4. 考虑移动设备IP变化的场景
5. OAuth2开放接口保持原有验证逻辑，不进行IP验证

## 实现状态

✅ **已完成**：
- TokenAuthenticationFilter中的IP验证
- OAuth2TokenApi接口扩展
- OAuth2TokenService服务实现
- 网关TokenAuthenticationFilter支持
- 数据库IP存储和验证

⚠️ **待考虑**：
- WebSocket连接的IP验证
- 移动设备IP变化的处理策略
- 特殊业务场景的IP验证需求
